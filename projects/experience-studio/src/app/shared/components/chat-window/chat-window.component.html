<div class="chat-wrapper d-flex flex-column" [ngClass]="theme">
  <div class="chat-messages flex-1 d-flex flex-column g-3" #chatScrollContainer>
    <ng-container *ngFor="let message of chatMessages">
      <!-- User Message Card with Markdown Support -->
      <awe-cards
        *ngIf="message.from === 'user'"
        variant="basic"
        size="small"
        class="user-card d-flex justify-content-end"
        [ngClass]="theme"
        [theme]="theme">
        <div content class="markdown-content">
          <!-- Display the image if it exists in the message -->
          <div class="selected-image m-2" *ngIf="message.imageDataUri">
            <div
              class="image-preview"
              (click)="showImagePreview(message.imageDataUri, 'Selected image')">
              <img [src]="message.imageDataUri" alt="Selected image" class="thumbnail-image" />
            </div>
          </div>
          <markdown [data]="getSanitizedMessageText(message.text)"></markdown>
        </div>
      </awe-cards>

      <!-- AI Message Card with Markdown Support -->
      <awe-cards
        *ngIf="message.from === 'ai'"
        variant="basic"
        size="small"
        class="ai-card d-flex justify-content-start"
        [ngClass]="[theme, message.hasSteps ? 'stepper-card' : '']"
        [theme]="theme">
        <div content class="markdown-content">
          <!-- Regular AI message content -->
          <div *ngIf="!message.hasSteps">
            <markdown [data]="getSanitizedMessageText(message.text)"></markdown>
            <!-- Show loading dots when stepper is active but not yet added to this message -->
            <div *ngIf="showStepper && status !== 'COMPLETED'" class="loading-dots d-flex align-items-center g-1">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </div>

          <!-- Stepper content if this message has steps -->
          <div *ngIf="message.hasSteps" class="stepper-content">
            <div *ngIf="message.text" class="stepper-intro">
              <markdown [data]="getSanitizedMessageText(message.text)"></markdown>
            </div>
            <app-vertical-stepper
              [theme]="theme"
              [progress]="progress"
              [progressDescription]="progressDescription"
              [status]="status"
              [restartable]="false"
              [projectId]="projectId"
              [jobId]="jobId"
              [useApi]="useApi"
              (stepUpdated)="onStepUpdated($event)"
              (retryStep)="onRetryStep($event)">
            </app-vertical-stepper>
          </div>
        </div>
      </awe-cards>
    </ng-container>

    <!-- Spacer to ensure content doesn't get hidden behind prompt bar -->
    <div class="prompt-spacer"></div>
  </div>

  <!-- Generating text above prompt bar -->
  <div *ngIf="shouldShowUIDesignLoadingIndicator()" class="generating-indicator d-flex justify-content-start align-items-center" [ngClass]="theme">
    <div class="generating-stepper d-flex align-items-center g-2">
      <!-- Exact same spinner from stepper component -->
      <div class="modern-loading-spinner d-flex align-items-center justify-content-center">
        <div class="spinner-ring"></div>
        <div class="spinner-core"></div>
      </div>
      <span class="generating-text">Generating...</span>
    </div>
  </div>

  <awe-prompt-bar
    class="mb-2"
    [theme]="theme"
    [defaultText]="defaultText"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    [(textValue)]="textValue"
    (iconClicked)="handleIconClick($event)"
    (enterPressed)="isCodeGenerationComplete ? enterPressed.emit() : null"
    [variant]="'chat-bot'"
    [class.disabled-prompt-bar]="!isCodeGenerationComplete">
  </awe-prompt-bar>
</div>

<!-- Image Preview Overlay -->
<div class="preview-overlay d-flex justify-content-center align-items-center" *ngIf="showPreview && previewImage">
  <div class="preview-content">
    <div class="preview-header d-flex justify-content-between align-items-center p-3">
      <div class="preview-title">{{ previewImage.name }}</div>
      <awe-icons
        iconName="awe_close"
        (click)="closeImagePreview()"
        role="button"
        tabindex="0"
        [attr.aria-label]="'Close preview'"
        [color]="getIconColor()"></awe-icons>
    </div>
    <div class="preview-body p-4 d-flex justify-content-center align-items-center">
      <img [src]="previewImage.url" [alt]="previewImage.name" />
    </div>
  </div>
</div>
