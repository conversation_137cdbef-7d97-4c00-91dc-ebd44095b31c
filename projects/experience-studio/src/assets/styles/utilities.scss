/*------------------------------------*\
  #ADDITIONAL UTILITY CLASSES
  Complementing the existing padding.scss utilities
\*------------------------------------*/

// Additional spacing utilities not covered in padding.scss
.p-1 {
  padding: 0.25rem !important;
}
.p-2 {
  padding: 0.5rem !important;
}
.p-3 {
  padding: 0.75rem !important;
}
.p-4 {
  padding: 1rem !important;
}
.p-5 {
  padding: 1.25rem !important;
}

.m-1 {
  margin: 0.25rem !important;
}
.m-2 {
  margin: 0.5rem !important;
}
.m-3 {
  margin: 0.75rem !important;
}
.m-4 {
  margin: 1rem !important;
}
.m-5 {
  margin: 1.25rem !important;
}

// Padding specific sides
.pt-1 { padding-top: 0.25rem !important; }
.pt-2 { padding-top: 0.5rem !important; }
.pt-3 { padding-top: 0.75rem !important; }
.pt-4 { padding-top: 1rem !important; }
.pt-5 { padding-top: 1.25rem !important; }

.pb-1 { padding-bottom: 0.25rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.pb-3 { padding-bottom: 0.75rem !important; }
.pb-4 { padding-bottom: 1rem !important; }
.pb-5 { padding-bottom: 1.25rem !important; }

.pl-1 { padding-left: 0.25rem !important; }
.pl-2 { padding-left: 0.5rem !important; }
.pl-3 { padding-left: 0.75rem !important; }
.pl-4 { padding-left: 1rem !important; }
.pl-5 { padding-left: 1.25rem !important; }

.pr-1 { padding-right: 0.25rem !important; }
.pr-2 { padding-right: 0.5rem !important; }
.pr-3 { padding-right: 0.75rem !important; }
.pr-4 { padding-right: 1rem !important; }
.pr-5 { padding-right: 1.25rem !important; }

// Margin specific sides
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 0.75rem !important; }
.mt-4 { margin-top: 1rem !important; }
.mt-5 { margin-top: 1.25rem !important; }

.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 0.75rem !important; }
.mb-4 { margin-bottom: 1rem !important; }
.mb-5 { margin-bottom: 1.25rem !important; }

.ml-1 { margin-left: 0.25rem !important; }
.ml-2 { margin-left: 0.5rem !important; }
.ml-3 { margin-left: 0.75rem !important; }
.ml-4 { margin-left: 1rem !important; }
.ml-5 { margin-left: 1.25rem !important; }

.mr-1 { margin-right: 0.25rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.mr-3 { margin-right: 0.75rem !important; }
.mr-4 { margin-right: 1rem !important; }
.mr-5 { margin-right: 1.25rem !important; }

// Flexbox utilities (complementing existing ones)
.d-flex {
  display: flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-1 {
  flex: 1 !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

// Gap utilities (complementing existing ones)
.g-1 {
  gap: 0.25rem !important;
}

.g-2 {
  gap: 0.5rem !important;
}

.g-3 {
  gap: 0.75rem !important;
}

.g-4 {
  gap: 1rem !important;
}

.g-5 {
  gap: 1.25rem !important;
}

// Width utilities
.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

// Height utilities
.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

// Position utilities
.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

// Text utilities
.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

// Overflow utilities
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-auto {
  overflow: auto !important;
}

// Border radius utilities
.rounded {
  border-radius: 0.25rem !important;
}

.rounded-lg {
  border-radius: 0.5rem !important;
}

.rounded-xl {
  border-radius: 0.75rem !important;
}

// Box sizing utility
.box-border {
  box-sizing: border-box !important;
}

// Cursor utilities
.cursor-pointer {
  cursor: pointer !important;
}

// User select utilities
.select-none {
  user-select: none !important;
}

.select-text {
  user-select: text !important;
}

// Z-index utilities
.z-10 {
  z-index: 10 !important;
}

.z-20 {
  z-index: 20 !important;
}

.z-50 {
  z-index: 50 !important;
}

.z-1000 {
  z-index: 1000 !important;
}
